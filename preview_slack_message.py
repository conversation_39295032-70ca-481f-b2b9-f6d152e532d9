#!/usr/bin/env python3
"""
Preview what the Slack message will look like
"""
from slack_webhook import SlackWebhookNotifier

def preview_slack_message():
    """Show what the Slack message will look like"""
    
    # Sample data from the real analysis
    report_data = {
        'total_new_fans': 15,
        'total_new_clicks': 978,
        'growing_sources': [
            ('Reels2024', 4, 272),
            ('reels-naominoface', 3, 167),
            ('reddit-babycheeksx', 2, 78),
            ('reels-lilfoxnaomi-aug-22', 2, 140),
            ('tiktok-aug-1-24', 2, 75),
            ('chive-aug-1-24', 2, 230)
        ],
        'stagnant_sources': []  # All links are active
    }
    
    ai_insights = "Scale successful Reels strategy given strong performance of Reels2024. Investigate reddit-babycheeksx's declining conversion despite good base rate."
    
    notifier = SlackWebhookNotifier()
    message = notifier.format_slack_message(report_data, ai_insights)
    
    print("📱 SLACK MESSAGE PREVIEW:")
    print("="*60)
    print(message)
    print("="*60)
    
    print("\n📋 This message will be sent to:")
    print(f"   Workspace: cheeksglobal.slack.com")
    print(f"   Channel ID: C090FEVA18D")
    print(f"   Channel URL: https://cheeksglobal.slack.com/archives/C090FEVA18D")
    
    print("\n🔧 To enable Slack notifications:")
    print("   1. Add 'chat:write' scope to your Slack app")
    print("   2. Reinstall the app")
    print("   3. Invite the bot to your channel")
    print("   4. Run: python3 daily_report_with_slack.py")

if __name__ == "__main__":
    preview_slack_message()
